import React, { useEffect } from 'react';
import { ActivityIndicator, View } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useDispatch } from 'react-redux';
import { setInfo, setAccessToken } from '../redux/action';

export default function AuthLoading({ navigation }: any) {
  const dispatch = useDispatch();

  useEffect(() => {
    const checkLogin = async () => {
      const idToken = await AsyncStorage.getItem('idToken');
      const userInfoString = await AsyncStorage.getItem('userInfo');
      const userInfo = userInfoString ? JSON.parse(userInfoString) : null;

      if (idToken && userInfo) {
        dispatch(setAccessToken(idToken));
        dispatch(setInfo(userInfo));
        navigation.reset({ index: 0, routes: [{ name: 'Home' }] });
      } else {
        navigation.reset({ index: 0, routes: [{ name: 'Selectloging' }] });
      }
    };
    checkLogin();
  }, [dispatch, navigation]);

  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
      <ActivityIndicator size="large" />
    </View>
  );
}
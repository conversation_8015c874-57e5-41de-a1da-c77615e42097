import Geolocation from 'react-native-geolocation-service';
import { PermissionsAndroid, Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  address?: string;
}

export interface RegionData {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

// Request location permission
export const requestLocationPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Access',
          message: 'Health Care Mobile needs location access to:\n\n• Show nearby health services\n• Save your location preferences\n• Provide personalized recommendations',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Deny',
          buttonPositive: 'Allow',
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn('Location permission error:', err);
      return false;
    }
  }
  return true; // iOS handled in Info.plist
};

// Save location to AsyncStorage
export const saveLocationToStorage = async (locationData: LocationData): Promise<void> => {
  try {
    await AsyncStorage.setItem('userLocation', JSON.stringify(locationData));
    console.log('✅ Location saved to AsyncStorage:', locationData);
  } catch (error) {
    console.error('❌ Error saving location to AsyncStorage:', error);
  }
};

// Get location from AsyncStorage
export const getLocationFromStorage = async (): Promise<LocationData | null> => {
  try {
    const locationString = await AsyncStorage.getItem('userLocation');
    if (locationString) {
      const locationData = JSON.parse(locationString);
      console.log('📍 Location retrieved from AsyncStorage:', locationData);
      return locationData;
    }
    return null;
  } catch (error) {
    console.error('❌ Error getting location from AsyncStorage:', error);
    return null;
  }
};

// Convert LocationData to RegionData for MapView
export const locationToRegion = (location: LocationData, delta: number = 0.015): RegionData => {
  return {
    latitude: location.latitude,
    longitude: location.longitude,
    latitudeDelta: delta,
    longitudeDelta: delta,
  };
};

// Get current location and save to AsyncStorage
export const getCurrentLocation = async (): Promise<LocationData | null> => {
  const hasPermission = await requestLocationPermission();
  
  if (!hasPermission) {
    Alert.alert(
      'Permission Required',
      'Location permission is required to show your current location.',
      [{ text: 'OK' }]
    );
    return null;
  }

  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      async (position) => {
        const locationData: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        };
        
        console.log('🌍 Current location obtained:', locationData);
        
        // Save location to AsyncStorage
        await saveLocationToStorage(locationData);
        
        resolve(locationData);
      },
      (error) => {
        console.error('❌ Error getting current location:', error);
        Alert.alert(
          'Location Error',
          'Unable to get your current location. Please check your GPS settings.',
          [{ text: 'OK' }]
        );
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
        forceRequestLocation: true,
        showLocationDialog: true,
      }
    );
  });
};

// Watch location changes and save to AsyncStorage
export const watchLocation = (callback?: (location: LocationData) => void) => {
  return Geolocation.watchPosition(
    async (position) => {
      const locationData: LocationData = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: position.timestamp,
      };
      
      console.log('📍 Location updated:', locationData);
      
      // Save to AsyncStorage
      await saveLocationToStorage(locationData);
      
      // Call callback if provided
      if (callback) {
        callback(locationData);
      }
    },
    (error) => {
      console.error('❌ Error watching location:', error);
    },
    {
      enableHighAccuracy: true,
      distanceFilter: 10, // Update every 10 meters
      interval: 5000, // Update every 5 seconds
      fastestInterval: 2000, // Fastest update every 2 seconds
    }
  );
};

// Clear location from AsyncStorage
export const clearLocationFromStorage = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem('userLocation');
    console.log('🗑️ Location cleared from AsyncStorage');
  } catch (error) {
    console.error('❌ Error clearing location from AsyncStorage:', error);
  }
};

// Check if we should show location permission dialog
export const shouldShowLocationPermissionDialog = async (): Promise<boolean> => {
  try {
    const locationPermission = await AsyncStorage.getItem('locationPermission');
    // Show dialog if never requested or explicitly set to not_requested
    return !locationPermission || locationPermission === 'not_requested';
  } catch (error) {
    console.error('❌ Error checking location permission status:', error);
    return true; // Default to showing dialog if we can't check
  }
};

// Get location permission status
export const getLocationPermissionStatus = async (): Promise<string> => {
  try {
    const status = await AsyncStorage.getItem('locationPermission');
    return status || 'not_requested';
  } catch (error) {
    console.error('❌ Error getting location permission status:', error);
    return 'not_requested';
  }
};

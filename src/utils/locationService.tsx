import Geolocation from 'react-native-geolocation-service';
import { PermissionsAndroid, Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  address?: string;
}



// Request location permission
export const requestLocationPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android') {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
        {
          title: 'Location Permission',
          message: 'This app needs access to your location to show nearby services.',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );
      return granted === PermissionsAndroid.RESULTS.GRANTED;
    } catch (err) {
      console.warn('Location permission error:', err);
      return false;
    }
  }
  return true; // iOS handled in Info.plist
};

// Save location to AsyncStorage
export const saveLocationToStorage = async (locationData: LocationData): Promise<void> => {
  try {
    const locationString = JSON.stringify(locationData);
    console.log('💾 Saving location to AsyncStorage...');
    console.log('📍 Location data to save:', locationData);
    console.log('📄 JSON string:', locationString);

    await AsyncStorage.setItem('userLocation', locationString);

    // Verify it was saved
    const savedData = await AsyncStorage.getItem('userLocation');
    console.log('✅ Location saved successfully!');
    console.log('🔍 Verification - saved data:', savedData);
    console.log('🔍 Verification - parsed data:', JSON.parse(savedData || '{}'));
  } catch (error) {
    console.error('❌ Error saving location to AsyncStorage:', error);
  }
};

// Get location from AsyncStorage
export const getLocationFromStorage = async (): Promise<LocationData | null> => {
  try {
    console.log('🔍 Getting location from AsyncStorage...');
    const locationString = await AsyncStorage.getItem('userLocation');
    console.log('📄 Raw location string from storage:', locationString);

    if (locationString) {
      const locationData = JSON.parse(locationString);
      console.log('📍 Parsed location data:', locationData);
      console.log('📍 Location details:', {
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp,
        timestampDate: locationData.timestamp ? new Date(locationData.timestamp).toLocaleString() : 'N/A',
        isValid: !!(locationData.latitude && locationData.longitude)
      });
      return locationData;
    } else {
      console.log('❌ No location string found in AsyncStorage');
      return null;
    }
  } catch (error) {
    console.error('❌ Error getting location from AsyncStorage:', error);
    return null;
  }
};

// Convert LocationData to Region for MapView
export const locationToRegion = (location: LocationData, delta: number = 0.015) => {
  return {
    latitude: location.latitude,
    longitude: location.longitude,
    latitudeDelta: delta,
    longitudeDelta: delta,
  };
};

// Get current location and save to AsyncStorage
export const getCurrentLocation = async (): Promise<LocationData | null> => {
  const hasPermission = await requestLocationPermission();
  
  if (!hasPermission) {
    Alert.alert(
      'Permission Required',
      'Location permission is required to show your current location.',
      [{ text: 'OK' }]
    );
    return null;
  }

  return new Promise((resolve, reject) => {
    Geolocation.getCurrentPosition(
      async (position) => {
        const locationData: LocationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp,
        };
        
        console.log('🌍 Current location obtained:', locationData);
        
        // Save location to AsyncStorage
        await saveLocationToStorage(locationData);
        
        resolve(locationData);
      },
      (error) => {
        console.error('❌ Error getting current location:', error);
        Alert.alert(
          'Location Error',
          'Unable to get your current location. Please check your GPS settings.',
          [{ text: 'OK' }]
        );
        reject(error);
      },
      {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 10000,
        forceRequestLocation: true,
        showLocationDialog: true,
      }
    );
  });
};

// Watch location changes and save to AsyncStorage
export const watchLocation = (callback?: (location: LocationData) => void) => {
  return Geolocation.watchPosition(
    async (position) => {
      const locationData: LocationData = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        accuracy: position.coords.accuracy,
        timestamp: position.timestamp,
      };
      
      console.log('📍 Location updated:', locationData);
      
      // Save to AsyncStorage
      await saveLocationToStorage(locationData);
      
      // Call callback if provided
      if (callback) {
        callback(locationData);
      }
    },
    (error) => {
      console.error('❌ Error watching location:', error);
    },
    {
      enableHighAccuracy: true,
      distanceFilter: 10, // Update every 10 meters
      interval: 5000, // Update every 5 seconds
      fastestInterval: 2000, // Fastest update every 2 seconds
    }
  );
};

// Clear location from AsyncStorage
export const clearLocationFromStorage = async (): Promise<void> => {
  try {
    await AsyncStorage.removeItem('userLocation');
    console.log('🗑️ Location cleared from AsyncStorage');
  } catch (error) {
    console.error('❌ Error clearing location from AsyncStorage:', error);
  }
};

// Check if we should show location permission dialog
export const shouldShowLocationPermissionDialog = async (): Promise<boolean> => {
  try {
    const locationPermission = await AsyncStorage.getItem('locationPermission');
    // Show dialog if never requested or explicitly set to not_requested
    return !locationPermission || locationPermission === 'not_requested';
  } catch (error) {
    console.error('❌ Error checking location permission status:', error);
    return true; // Default to showing dialog if we can't check
  }
};

// Get location permission status
export const getLocationPermissionStatus = async (): Promise<string> => {
  try {
    const status = await AsyncStorage.getItem('locationPermission');
    return status || 'not_requested';
  } catch (error) {
    console.error('❌ Error getting location permission status:', error);
    return 'not_requested';
  }
};

// Debug function to show all AsyncStorage data
export const debugAsyncStorage = async (): Promise<void> => {
  try {
    console.log('🔍 === ASYNCSTORAGE DEBUG ===');

    // Get all keys
    const keys = await AsyncStorage.getAllKeys();
    console.log('📋 All AsyncStorage keys:', keys);

    // Get all values
    const stores = await AsyncStorage.multiGet(keys);
    stores.forEach(([key, value]) => {
      console.log(`🔑 ${key}:`, value);

      // Try to parse JSON values
      if (value) {
        try {
          const parsed = JSON.parse(value);
          console.log(`📄 ${key} (parsed):`, parsed);
        } catch (e) {
          console.log(`📄 ${key} (string):`, value);
        }
      }
    });

    console.log('🔍 === END ASYNCSTORAGE DEBUG ===');
  } catch (error) {
    console.error('❌ Error debugging AsyncStorage:', error);
  }
};

// Test function to save a sample location
export const saveSampleLocation = async (): Promise<void> => {
  const sampleLocation: LocationData = {
    latitude: 13.7563, // Bangkok
    longitude: 100.5018,
    accuracy: 10,
    timestamp: Date.now(),
  };

  console.log('🧪 Saving sample location for testing...');
  await saveLocationToStorage(sampleLocation);
};

// Force get and save current location
export const forceGetAndSaveLocation = async (): Promise<LocationData | null> => {
  console.log('🚀 Force getting current location...');

  try {
    // Check permission first
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      console.log('❌ No location permission, cannot get location');
      return null;
    }

    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        async (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp,
          };

          console.log('🌍 Got current location:', locationData);

          // Save to AsyncStorage
          await saveLocationToStorage(locationData);

          resolve(locationData);
        },
        (error) => {
          console.error('❌ Error getting current location:', error);
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
          forceRequestLocation: true,
          showLocationDialog: true,
        }
      );
    });
  } catch (error) {
    console.error('❌ Error in forceGetAndSaveLocation:', error);
    return null;
  }
};

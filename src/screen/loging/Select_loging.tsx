import {
  View,
  Platform,
  Image,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import React from 'react';
import {
  useTheme as useRNPTheme,
  Button,
  Text,
  Divider,
} from 'react-native-paper';
import Images from '../../utils/ImageManager';
import styles from '../../components/CustomStyle';
import CustomIcon from '../../components/CustomIcon';
import AppleSignIn from '../../components/appleSingIn/AppleSignIn';
import GoogleSignIn from '../../components/googleSignIn/GoogleSignIn';
import FacebookSignIn from '../../components/facebookSignIn/FacebookSignIn';
import SnapRecommend from '../../components/snapCarousel/snapRecommend';

export default function Select_loging({navigation}: any) {
  const handleEmailSigIn = (userData: any) => {
    console.log('Email Sign In Success', userData);
    navigation.navigate('BottomTap');
  };
  const handleEmailError = (error: any) => {
    console.log('Email Sign In Error', error);
  };
  const handlePhoneSigIn = (userData: any) => {
    console.log('Phone Sign In Success', userData);
    navigation.navigate('PhoneSignInSereen');
  };
  const hadlePhoneSigInError = (error: any) => {
    console.log('Phone Sign In Error');
  };
  const handleAppleSignIn = (userData: any) => {
    console.log('Apple Sign In Success:', userData);
    navigation.navigate('Home');
  };
  const handleAppleSignInError = (error: any) => {
    console.error('Apple Sign In Error:', error);
  };
  const handleGoogleSignIn = (userData: any) => {
    // console.log('Google Sign In Success:', userData);
    if (userData && userData.type !== 'cancelled' && userData.data) {
      navigation.reset({
        index: 0,
        routes: [{name: 'Home'}],
      });
    }
  };
  const handleGoogleSignInError = (error: any) => {
    console.error('Google Sign In Error:', error);
  };
  const handleFacebookSignIn = (userData: any) => {
    console.log('Facebook Sign In Success:', userData);
    navigation.navigate('BottomTap');
  };
  const handleFacebookSignInError = (error: any) => {
    console.error('Facebook Sign In Error:', error);
  };

  return (
    <SafeAreaView style={[styles.constainerDefault]}>
      <View style={styles.continueManageLogin}>
        {/* <View style={styles.logoContainer}>
          <Image
            source={Images.logoApp}
            style={styles.logoApp}
            resizeMode="contain"
          />
        </View> */}
        <SnapRecommend />
        {/* <Text style={styles.title}>Welcome to</Text>
        <Text style={styles.title}>Health care service</Text>
        <Text style={styles.titleBody}>Let us take care of you</Text> */}

        {/* <Button
          mode="contained"
          onPress={() => navigation.navigate('EmailLogin')}
          style={styles.emaiPhonelButton}
          icon={({size, color}) => (
            <CustomIcon name="email" size={size} color={color} />
          )}>
          Continue with Email
        </Button> */}

        <Button
          mode="contained"
          onPress={() => navigation.navigate('PhoneSignInSereen')}
          style={styles.emaiPhonelButton}
          icon={({size, color}) => (
            <CustomIcon name="phone" size={size} color={color} />
          )}>
          Continue with Phone number
        </Button>

        <View style={styles.dividerContainer}>
          <Divider style={styles.divider} />
          <Text style={styles.orText}>OR</Text>
          <Divider style={styles.divider} />
        </View>

        <View style={styles.containerRowCenter}>
          {Platform.OS === 'ios' && (
            <AppleSignIn
              onSignIn={handleAppleSignIn}
              onError={handleAppleSignInError}
              logo={Images.appleLogo}
            />
          )}
          <View style={styles.marginBottom5} />

          <GoogleSignIn
            onSignIn={handleGoogleSignIn}
            onError={handleGoogleSignInError}
            logo={Images.googleLogo}
          />
          <View style={styles.marginBottom5} />

          <FacebookSignIn
            onSignIn={handleFacebookSignIn}
            onError={handleFacebookSignInError}
            logo={Images.facebookLogo}
          />
        </View>

        <View style={styles.skipButton}>
          <Text style={styles.titleMembership}>Join us as a partner</Text>
          <TouchableOpacity>
            <Text style={styles.titleMembershipGo}>Apply for membership</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.titlePolicy}>
          By clicking continue, you agree to our Terms of Service
        </Text>
        <View style={styles.containerRowCenter}>
          <Text style={styles.titlePolicy}>and</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Policy')}>
            <Text style={styles.titlePolicyGo}>Privacy Policy</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}

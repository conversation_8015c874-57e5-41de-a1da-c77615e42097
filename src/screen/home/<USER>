import {Text, View, ScrollView, Alert} from 'react-native';
import React from 'react';
import Images from '../../utils/ImageManager';
import bgColor from '../../utils/bgColor';
import CustomIcon from '../../components/CustomIcon';
import styles from '../../components/CustomStyle';
import {Avatar, Button} from 'react-native-paper';
import {useSelector, useDispatch} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function Profile({navigation}: any) {
  const dispatch = useDispatch();
  const info = useSelector((state: any) => state.info);
  const profileName = info?.data?.user?.name || '';
  const imageProfile = info?.data?.user?.photo || '';

  const alertLogout = () =>
    Alert.alert('Alert Title', 'My Alert Msg', [
      {
        text: 'Cancel',
        onPress: () => console.log('Cancel Pressed'),
        style: 'cancel',
      },
      {text: 'OK', onPress: () => handleLogout()},
    ]);
  const handleLogout = async () => {
    dispatch({type: 'LOGOUT'});
    dispatch({type: 'SET_INFO', payload: null});
    dispatch({type: 'SET_ACCESS_TOKEN', payload: ''});
    await AsyncStorage.removeItem('idToken');
    await AsyncStorage.removeItem('userInfo');
    navigation.reset({
      index: 0,
      routes: [{name: 'Selectloging'}],
    });
  };

  return (
    <ScrollView
      style={styles.constainerDefault}
      showsVerticalScrollIndicator={false}>
      <Button style={styles.buttomAccount} mode="contained">
        <View style={{alignItems: 'center'}}>
          <Avatar.Image
            size={100}
            source={{uri: imageProfile}}
            style={{marginRight: 10}}
          />
          <Text style={{fontSize: 18, fontWeight: 'bold', marginTop: 10}}>
            {profileName}
          </Text>
        </View>
      </Button>

      <View
        style={{
          flexDirection: 'row',
          marginTop: 10,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,

          elevation: 3,
        }}>
        <Button style={styles.buttomRowSetting} mode="contained">
          <View style={{alignItems: 'flex-start'}}>
            <CustomIcon
              name="edit-document"
              size={24}
              color={bgColor.primary}
            />
            <Text style={{fontSize: 14, marginTop: 5}}>Edit Account</Text>
          </View>
        </Button>
        <View style={{margin: 8}} />
        <Button style={styles.buttomRowSetting} mode="contained">
          <View style={{alignItems: 'flex-start'}}>
            <CustomIcon name="recommend" size={24} color={bgColor.primary} />
            <Text style={{fontSize: 14, marginTop: 5}}>
              Recommen Exercise Exercises
            </Text>
          </View>
        </Button>
      </View>

      <View
        style={{
          flexDirection: 'row',
          marginTop: 10,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,

          elevation: 3,
        }}>
        <Button style={styles.buttomRowSetting} mode="contained">
          <View style={{alignItems: 'flex-start'}}>
            <CustomIcon name="calendar-month" size={24} color={bgColor.alert} />
            <Text style={{fontSize: 14, marginTop: 5}}>
              Appointment Schedule
            </Text>
          </View>
        </Button>
        <View style={{margin: 8}} />
        <Button style={styles.buttomRowSetting} mode="contained">
          <View style={{alignItems: 'flex-start'}}>
            <CustomIcon
              name="collections-bookmark"
              size={24}
              color={bgColor.Favorite}
            />
            <Text style={{fontSize: 14, marginTop: 5}}>Favorite</Text>
          </View>
        </Button>
      </View>

      <View
        style={{
          flexDirection: 'row',
          marginTop: 10,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,

          elevation: 3,
        }}>
        <Button
          style={styles.buttomRowSetting}
          mode="contained"
          onPress={() => navigation.navigate('SettingsApp')}>
          <View style={{alignItems: 'flex-start'}}>
            <CustomIcon name="moving" size={24} color={bgColor.success} />
            <Text style={{fontSize: 14, marginTop: 5}}>Treatment Progress</Text>
          </View>
        </Button>
        <View style={{margin: 8}} />
        <Button
          style={styles.buttomRowSetting}
          mode="contained"
          onPress={() => navigation.navigate('SettingsApp')}>
          <View style={{alignItems: 'flex-start'}}>
            <CustomIcon name="history" size={24} color={bgColor.primary} />
            <Text style={{fontSize: 14, marginTop: 5}}>Medical History</Text>
          </View>
        </Button>
      </View>

      <View
        style={{
          flexDirection: 'row',
          marginTop: 10,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,

          elevation: 3,
        }}>
        <Button
          style={styles.buttomRowSetting}
          mode="contained"
          onPress={() => navigation.navigate('SettingsApp')}>
          <View style={{alignItems: 'flex-start'}}>
            <CustomIcon name="manage-accounts" size={24} color="gray" />
            <Text style={{fontSize: 14, marginTop: 5}}>Setting</Text>
          </View>
        </Button>
        <View style={{margin: 8}} />
        <Button
          style={styles.buttomRowSetting}
          mode="contained"
          onPress={() => navigation.navigate('SettingsApp')}>
          <View style={{alignItems: 'flex-start'}}>
            <CustomIcon name="report" size={24} color={bgColor.report} />
            <Text style={{fontSize: 14, marginTop: 5}}>Report a problem</Text>
          </View>
        </Button>
      </View>

      <View
        style={{
          marginTop: 10,
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 1,
          },
          shadowOpacity: 0.22,
          shadowRadius: 2.22,

          elevation: 3,
        }}>
        <Button
          style={styles.buttomLogout}
          mode="contained"
          onPress={alertLogout}>
          <View style={{flexDirection: 'row', alignItems: 'center'}}>
            <CustomIcon name="logout" size={24} color={bgColor.alert} />
            <Text style={{fontSize: 14, marginLeft: 5}}>Logout</Text>
          </View>
        </Button>
      </View>
    </ScrollView>
  );
}

import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import React, {useState, useCallback, useRef} from 'react';
import {useSelector, useDispatch} from 'react-redux';
import styles from '../../components/CustomStyle';

import SnapCarousel from '../../components/snapCarousel/snapCarousel';
import ListPhtsical from '../../components/listPhysical/listPhtsical';
import FilterPhysical from '../../components/filter/filterPhysical';

export default function Home({navigation}: any) {
  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}>
      <SnapCarousel />
      <ListPhtsical navigation={navigation} />
    </ScrollView>
  );
}

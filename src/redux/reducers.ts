// reducer.js
const initialState = {
  accessToken: null,
  info: null,
};

type Action = {
  type: string;
  payload?: any;
};

const rootReducer = (state = initialState, action: Action) => {
  switch (action.type) {
    case 'SET_LOG_OUT':
      return {...state, info: null, accessToken: null};
    case 'SET_ACCESS_TOKEN':
      return {...state, accessToken: action.payload};
    case 'SET_INFO':
      return {...state, info: action.payload};
    default:
      return state;
  }
};

export default rootReducer;

import {StyleSheet, Text, View, Platform} from 'react-native';
import MapView, {PROVIDER_GOOGLE, Region} from 'react-native-maps';
import React, {useEffect, useState} from 'react';
import {
  locationToRegion,
  getLocationFromStorage,
} from '../../utils/locationService';

export default function TarckingUser() {
  const [region, setRegion] = useState<Region>({
    latitude: 13.7563, // Default to Bangkok
    longitude: 100.5018,
    latitudeDelta: 0.015,
    longitudeDelta: 0.0121,
  });

  useEffect(() => {
    const fetchLocation = async () => {
      const location = await getLocationFromStorage();
      console.log(location);
      if (location) {
        setRegion(locationToRegion(location));
      }
    };
    fetchLocation();
  }, []);

  return (
    <View style={styles.container}>
      <MapView
        provider={Platform.OS === 'ios' ? undefined : PROVIDER_GOOGLE}
        style={styles.map}
        region={region}
        onRegionChangeComplete={newRegion => setRegion(newRegion)}
        loadingEnabled={true}
        loadingIndicatorColor="#666666"
        loadingBackgroundColor="#eeeeee"
        showsUserLocation={true}
        followsUserLocation={true}
        showsMyLocationButton={true}
        showsCompass={true}
        showsScale={true}
        showsBuildings={true}
        showsIndoors={true}
        showsIndoorLevelPicker={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});

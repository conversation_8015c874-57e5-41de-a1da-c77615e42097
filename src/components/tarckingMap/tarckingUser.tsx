import {StyleSheet, Text, View, Platform} from 'react-native';
import MapView, {PROVIDER_GOOGLE} from 'react-native-maps';
import React, {useEffect, useState} from 'react';
import {
  locationToRegion,
  getLocationFromStorage,
} from '../../utils/locationService';

export default function TarckingUser() {
  const [region, setRegion] = useState<any>(null);
  console.log(region);
  

  useEffect(() => {
    const fetchLocation = async () => {
      const location = await getLocationFromStorage();
      if (location) {
        setRegion(locationToRegion(location));
      }
    };
    fetchLocation();
  }, []);

  return (
    <View style={styles.container}>
      <MapView
        provider={Platform.OS === 'ios' ? undefined : PROVIDER_GOOGLE}
        style={styles.map}
        region={region}
        loadingEnabled></MapView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});

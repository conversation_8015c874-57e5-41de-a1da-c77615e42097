import {StyleSheet, View, Platform} from 'react-native';
import MapView, {PROVIDER_GOOGLE, Region} from 'react-native-maps';
import React, {useEffect, useState} from 'react';
import {
  locationToRegion,
  getLocationFromStorage,
  debugAsyncStorage,
} from '../../utils/locationService';

export default function TarckingUser() {
  const [region, setRegion] = useState<Region>({
    latitude: 13.7563, // Default to Bangkok
    longitude: 100.5018,
    latitudeDelta: 0.015,
    longitudeDelta: 0.0121,
  });

  useEffect(() => {
    const fetchLocation = async () => {
      console.log('🔍 Fetching location from AsyncStorage...');

      // Debug all AsyncStorage data
      await debugAsyncStorage();

      const location = await getLocationFromStorage();

      console.log('📍 Location from AsyncStorage:', location);
      console.log('📍 Location details:', {
        latitude: location?.latitude,
        longitude: location?.longitude,
        accuracy: location?.accuracy,
        timestamp: location?.timestamp,
        timestampDate: location?.timestamp ? new Date(location.timestamp).toLocaleString() : 'N/A'
      });

      if (location) {
        const newRegion = locationToRegion(location);
        console.log('🗺️ Converting to region:', newRegion);
        setRegion(newRegion);
        console.log('✅ Region updated successfully');
      } else {
        console.log('❌ No location found in AsyncStorage, using default Bangkok region');
      }
    };
    fetchLocation();
  }, []);

  return (
    <View style={styles.container}>
      <MapView
        provider={Platform.OS === 'ios' ? undefined : PROVIDER_GOOGLE}
        style={styles.map}
        region={region}
        onRegionChangeComplete={newRegion => setRegion(newRegion)}
        loadingEnabled={true}
        loadingIndicatorColor="#666666"
        loadingBackgroundColor="#eeeeee"
        showsUserLocation={true}
        followsUserLocation={true}
        showsMyLocationButton={true}
        showsCompass={true}
        showsScale={true}
        showsBuildings={true}
        showsIndoors={true}
        showsIndoorLevelPicker={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});

import { StyleSheet, Text, View } from 'react-native'
import MapView, { PROVIDER_GOOGLE, Region } from 'react-native-maps';
import React, { useEffect, useState } from 'react'
import {
  getCurrentLocation,
  getLocationFromStorage,
  locationToRegion,
  LocationData
} from '../../utils/locationService';

export default function TarckingUser() {
  const [region, setRegion] = useState<Region>({
    latitude: 13.7563, // Default to Bangkok
    longitude: 100.5018,
    latitudeDelta: 0.015,
    longitudeDelta: 0.0121,
  });

  const [loading, setLoading] = useState(true);

  // Load location on component mount
  useEffect(() => {
    const loadLocation = async () => {
      try {
        // First try to get saved location from AsyncStorage
        const savedLocation = await getLocationFromStorage();

        if (savedLocation) {
          const savedRegion = locationToRegion(savedLocation);
          setRegion(savedRegion);
          console.log('📍 Using saved location:', savedLocation);
        }

        // Then try to get current location and update
        const currentLocation = await getCurrentLocation();
        if (currentLocation) {
          const currentRegion = locationToRegion(currentLocation);
          setRegion(currentRegion);
          console.log('🌍 Updated to current location:', currentLocation);
        }
      } catch (error) {
        console.error('❌ Error loading location:', error);
      } finally {
        setLoading(false);
      }
    };

    loadLocation();
  }, []);

  const handleRegionChange = async (newRegion: Region) => {
    setRegion(newRegion);

    // Save the new region as location data
    const locationData: LocationData = {
      latitude: newRegion.latitude,
      longitude: newRegion.longitude,
      accuracy: 0,
      timestamp: Date.now(),
    };

    // You can uncomment this if you want to save every region change
    // await saveLocationToStorage(locationData);
  };

  return (
    <View style={styles.container}>
      {loading && (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading location...</Text>
        </View>
      )}
      <MapView
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        region={region}
        onRegionChangeComplete={handleRegionChange}
        showsUserLocation={true}
        showsMyLocationButton={true}
        loadingEnabled
      >
      </MapView>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  loadingContainer: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    zIndex: 1000,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginHorizontal: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
});
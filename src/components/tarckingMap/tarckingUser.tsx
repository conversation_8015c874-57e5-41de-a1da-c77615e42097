import {StyleSheet, Text, View, Platform} from 'react-native';
import MapView, {PROVIDER_GOOGLE, Region} from 'react-native-maps';
import React, {useEffect, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  locationToRegion,
  getLocationFromStorage,
  getCurrentLocation,
} from '../../utils/locationService';

export default function TarckingUser() {
  // Default region (Bangkok)
  const [region, setRegion] = useState<Region>({
    latitude: 13.7563,
    longitude: 100.5018,
    latitudeDelta: 0.015,
    longitudeDelta: 0.0121,
  });

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadLocation = async () => {
      try {
        // Check location permission
        const locationPermission = await AsyncStorage.getItem('locationPermission');
        console.log('📍 Location permission status:', locationPermission);

        if (locationPermission === 'granted') {
          // First try to get saved location
          const savedLocation = await getLocationFromStorage();
          if (savedLocation) {
            const savedRegion = locationToRegion(savedLocation);
            setRegion(savedRegion);
            console.log('📍 Using saved location:', savedLocation);
          }

          // Then try to get current location
          try {
            const currentLocation = await getCurrentLocation();
            if (currentLocation) {
              const currentRegion = locationToRegion(currentLocation);
              setRegion(currentRegion);
              console.log('🌍 Updated to current location:', currentLocation);
            }
          } catch (error) {
            console.log('⚠️ Could not get current location, using saved/default location');
          }
        } else {
          console.log('📍 Location permission not granted, using default location (Bangkok)');
        }
      } catch (error) {
        console.error('❌ Error loading location:', error);
      } finally {
        setLoading(false);
      }
    };

    loadLocation();
  }, []);

  return (
    <View style={styles.container}>
      {loading && (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading map...</Text>
        </View>
      )}
      <MapView
        provider={Platform.OS === 'ios' ? undefined : PROVIDER_GOOGLE}
        style={styles.map}
        region={region}
        onRegionChangeComplete={(newRegion) => setRegion(newRegion)}
        showsUserLocation={true}
        showsMyLocationButton={true}
        followsUserLocation={false}
        showsCompass={true}
        showsScale={true}
        loadingEnabled={true}
        loadingIndicatorColor="#007AFF"
        loadingBackgroundColor="#FFFFFF"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  loadingContainer: {
    position: 'absolute',
    top: 50,
    left: 0,
    right: 0,
    zIndex: 1000,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginHorizontal: 20,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
  },
});

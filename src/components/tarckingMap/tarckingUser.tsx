import { StyleSheet, Text, View } from 'react-native'
import MapView, { PROVIDER_GOOGLE } from 'react-native-maps';
import React ,{useEffect, useState} from 'react'
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function TarckingUser() {
  
  
  return (
    <View style={styles.container}>
     <MapView
      //  provider={PROVIDER_GOOGLE}
       style={styles.map}
       region={{
         latitude: 37.78825,
         longitude: -122.4324,
         latitudeDelta: 0.015,
         longitudeDelta: 0.0121,
       }}
       loadingEnabled
     >
     </MapView>
   </View>
  )
}

const styles = StyleSheet.create({
 container: {
   ...StyleSheet.absoluteFillObject,
   flex: 1,
   justifyContent: 'flex-end',
   alignItems: 'center',
 },
 map: {
   ...StyleSheet.absoluteFillObject,
 },
});
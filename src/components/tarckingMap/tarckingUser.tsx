import {StyleSheet, View, Platform, TouchableOpacity, Text, Alert, ActionSheetIOS,} from 'react-native';
import MapView, {PROVIDER_GOOGLE, Region} from 'react-native-maps';
import React, {useEffect, useState} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  locationToRegion,
  getLocationFromStorage,
  debugAsyncStorage,
  saveSampleLocation,
  forceGetAndSaveLocation,
  checkAndFixLocationPermission,
  checkSystemLocationPermission,
} from '../../utils/locationService';

export default function TarckingUser() {
  const [region, setRegion] = useState<Region>({
    latitude: 13.7563, // Default to Bangkok
    longitude: 100.5018,
    latitudeDelta: 0.015,
    longitudeDelta: 0.0121,
  });

  useEffect(() => {
    const fetchLocation = async () => {
      console.log('🔍 Fetching location from AsyncStorage...');

      // Debug all AsyncStorage data
      await debugAsyncStorage();

      const location = await getLocationFromStorage();

      console.log('📍 Location from AsyncStorage:', location);
      console.log('📍 Location details:', {
        latitude: location?.latitude,
        longitude: location?.longitude,
        accuracy: location?.accuracy,
        timestamp: location?.timestamp,
        timestampDate: location?.timestamp ? new Date(location.timestamp).toLocaleString() : 'N/A'
      });

      if (location) {
        const newRegion = locationToRegion(location);
        console.log('🗺️ Converting to region:', newRegion);
        setRegion(newRegion);
        console.log('✅ Region updated successfully');
      } else {
        console.log('❌ No location found in AsyncStorage');
        console.log('🧪 Trying to save sample location...');

        // Try to save sample location first
        await saveSampleLocation();

        // Then try to get real location
        console.log('🚀 Trying to get real current location...');
        try {
          const currentLocation = await forceGetAndSaveLocation();
          if (currentLocation) {
            const newRegion = locationToRegion(currentLocation);
            console.log('🗺️ Got real location, updating region:', newRegion);
            setRegion(newRegion);
          } else {
            console.log('❌ Could not get real location, using default Bangkok region');
          }
        } catch (error) {
          console.log('❌ Error getting real location:', error);
          console.log('📍 Using default Bangkok region');
        }
      }
    };
    fetchLocation();
  }, []);

  const handleTestSaveLocation = async () => {
    console.log('🧪 Testing save sample location...');
    await saveSampleLocation();
    Alert.alert('Test', 'Sample location saved! Check console logs.');
  };

  const handleGetRealLocation = async () => {
    console.log('🚀 Testing get real location...');
    try {
      const location = await forceGetAndSaveLocation();
      if (location) {
        const newRegion = locationToRegion(location);
        setRegion(newRegion);
        Alert.alert('Success', `Got location: ${location.latitude}, ${location.longitude}`);
      } else {
        Alert.alert('Failed', 'Could not get location. Check permissions.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get location. Check console logs.');
    }
  };

  const handleFixPermission = async () => {
    console.log('🔧 Testing fix permission...');
    try {
      const fixed = await checkAndFixLocationPermission();
      if (fixed) {
        Alert.alert('Success', 'Location permission is now granted!');
      } else {
        Alert.alert('Failed', 'Could not fix location permission. Please check device settings manually.');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to fix permission. Check console logs.');
    }
  };

  const handleCheckPermission = async () => {
    console.log('🔍 Checking permission status...');
    try {
      const systemPermission = await checkSystemLocationPermission();
      const appPermission = await AsyncStorage.getItem('locationPermission');

      Alert.alert(
        'Permission Status',
        `App Permission: ${appPermission || 'not_requested'}\nSystem Permission: ${systemPermission ? 'granted' : 'denied'}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to check permission. Check console logs.');
    }
  };

  return (
    <View style={styles.container}>
      {/* Debug buttons */}
      {/* <View style={styles.debugContainer}>
        <TouchableOpacity style={styles.debugButton} onPress={handleTestSaveLocation}>
          <Text style={styles.debugButtonText}>Save Sample</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.debugButton} onPress={handleCheckPermission}>
          <Text style={styles.debugButtonText}>Check Permission</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.debugButton} onPress={handleFixPermission}>
          <Text style={styles.debugButtonText}>Fix Permission</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.debugButton} onPress={handleGetRealLocation}>
          <Text style={styles.debugButtonText}>Get Real Location</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.debugButton} onPress={debugAsyncStorage}>
          <Text style={styles.debugButtonText}>Debug Storage</Text>
        </TouchableOpacity>
      </View> */}

      <MapView
        provider={Platform.OS === 'ios' ? undefined : PROVIDER_GOOGLE}
        style={styles.map}
        region={region}
        onRegionChangeComplete={newRegion => setRegion(newRegion)}
        loadingEnabled={true}
        loadingIndicatorColor="#666666"
        loadingBackgroundColor="#eeeeee"
        showsUserLocation={true}
        followsUserLocation={true}
        showsMyLocationButton={true}
        showsCompass={true}
        showsScale={true}
        showsBuildings={true}
        showsIndoors={true}
        showsIndoorLevelPicker={true}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  debugContainer: {
    position: 'absolute',
    top: 50,
    right: 10,
    zIndex: 1000,
    flexDirection: 'column',
    gap: 8,
  },
  debugButton: {
    backgroundColor: 'rgba(0, 122, 255, 0.9)',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    minWidth: 120,
  },
  debugButtonText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
});

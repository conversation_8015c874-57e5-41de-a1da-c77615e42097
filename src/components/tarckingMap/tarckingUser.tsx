import {StyleSheet, Text, View, Platform} from 'react-native';
import MapView, {PROVIDER_GOOGLE} from 'react-native-maps';
import React, {useEffect, useState} from 'react';
import {
  locationToRegion,
  getLocationFromStorage,
} from '../../utils/locationService';

export default function TarckingUser() {
  const [region, setRegion] = useState<any>(null);

  

  useEffect(() => {
    const fetchLocation = async () => {
      const location = await getLocationFromStorage();
      console.log('getLocationFromStorage', location);
      
        console.log(location);
      if (location) {
        setRegion(locationToRegion(location));
      }
    };
    fetchLocation();
  }, []);

  return (
    <View style={styles.container}>
      <MapView
        provider={PROVIDER_GOOGLE}
        style={styles.map}
        region={region}
        loadingEnabled></MapView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    flex: 1,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
});

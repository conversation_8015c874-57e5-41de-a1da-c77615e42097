import React, {useState} from 'react';
import {StyleSheet, View, TouchableOpacity, Text} from 'react-native';
import {MultiSelect} from 'react-native-element-dropdown';
import CustomIcon from '../CustomIcon';
import bgColor from '../../utils/bgColor';

export default function DropDownLocation() {
  const data = [
    {label: 'Bangkok', value: 'bangkok', icon: 'location-city'},
    {label: 'Chiang Mai', value: 'chiangmai', icon: 'location-city'},
    {label: 'Phuket', value: 'phuket', icon: 'location-city'},
    {label: 'Pattaya', value: 'pattaya', icon: 'location-city'},
    {label: 'Krabi', value: 'krabi', icon: 'location-city'},
    {label: 'Koh <PERSON>', value: 'kohsamui', icon: 'location-city'},
    {label: 'Hua Hin', value: 'huahin', icon: 'location-city'},
    {label: 'Ayutthaya', value: 'ayutthaya', icon: 'location-city'},
  ];

  const [selected, setSelected] = useState([]);

  const renderEmptyComponent = () => (
    <View style={customStyles.emptyContainer}>
      <CustomIcon name="location-off" size={48} color={bgColor.grayMedium} />
      <Text style={customStyles.emptyText}>No locations found</Text>
      <Text style={customStyles.emptySubText}>
        Try adjusting your search terms
      </Text>
    </View>
  );

  const renderItem = (item: any, isSelected?: boolean) => {
    return (
      <View style={{paddingHorizontal: 10}}>
        <View
          style={[
            customStyles.dropdownItem,
            isSelected && customStyles.selectedDropdownItem,
          ]}>
          <View style={customStyles.itemContent}>
            <CustomIcon
              name={item.icon}
              size={20}
              color={isSelected ? bgColor.white : bgColor.primary}
            />
            <Text
              style={[
                customStyles.itemText,
                isSelected && customStyles.selectedItemTextInDropdown,
              ]}>
              {item.label}
            </Text>
          </View>
          <CustomIcon
            name={isSelected ? 'check' : 'chevron-right'}
            size={16}
            color={isSelected ? bgColor.white : bgColor.grayMedium}
          />
        </View>
      </View>
    );
  };
  return (
    <View style={customStyles.container}>
      <View style={customStyles.headerContainer}>
        <View>
          <Text style={customStyles.label}>Select Locations Filter</Text>
          <Text style={customStyles.description}>
            Choose one or more locations for your
          </Text>
        </View>
        {selected.length > 0 && (
          <TouchableOpacity
            style={customStyles.clearButton}
            onPress={() => setSelected([])}>
            <Text style={customStyles.clearButtonText}>
              Clear All ({selected.length})
            </Text>
          </TouchableOpacity>
        )}
      </View>
      <MultiSelect
        mode="modal"
        style={customStyles.dropdown}
        containerStyle={customStyles.dropdownContainer}
        placeholderStyle={customStyles.placeholderStyle}
        selectedTextStyle={customStyles.selectedTextStyle}
        inputSearchStyle={customStyles.inputSearchStyle}
        iconStyle={customStyles.iconStyle}
        itemContainerStyle={customStyles.itemContainer}
        data={data}
        labelField="label"
        valueField="value"
        placeholder="Select Location"
        value={selected}
        search
        searchPlaceholder="Search locations..."
        maxHeight={300}
        onChange={(item: any) => {
          setSelected(item);
        }}
        renderLeftIcon={() => (
          <CustomIcon name="location-on" size={20} color={bgColor.primary} />
        )}
        renderItem={(item, isSelected) => renderItem(item, isSelected)}
        renderRightIcon={() => (
          <CustomIcon name="expand-more" size={20} color={bgColor.grayMedium} />
        )}
        flatListProps={{
          ListEmptyComponent: renderEmptyComponent,
        }}
        renderSelectedItem={(item, unSelect) => (
          <TouchableOpacity
            onPress={() => unSelect && unSelect(item)}
            style={customStyles.selectedItemContainer}>
            <View style={customStyles.selectedItem}>
              <CustomIcon name={item.icon} size={16} color={bgColor.primary} />
              <Text style={customStyles.selectedItemText}>{item.label}</Text>
              <CustomIcon name="close" size={16} color={bgColor.grayMedium} />
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );
}

const customStyles = StyleSheet.create({
  container: {
    marginVertical: 8,
    paddingHorizontal: 20,
    
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  label: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: bgColor.grayMedium,
    marginBottom: 8,
    lineHeight: 20,
  },
  clearButton: {
    backgroundColor: bgColor.alert + '15',
    borderColor: bgColor.alert,
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  clearButtonText: {
    fontSize: 12,
    color: bgColor.alert,
    fontWeight: '600',
  },
  dropdown: {
    height: 50,
    backgroundColor: bgColor.white,
    borderRadius: 12,
    paddingHorizontal: 16,
     shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  placeholderStyle: {
    fontSize: 16,
    color: bgColor.grayMedium,
    marginLeft: 8,
  },
  selectedTextStyle: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: bgColor.grayLight,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  dropdownContainer: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: bgColor.grayLight,
    backgroundColor: bgColor.white,
    maxHeight: 300,
  },
  itemContainer: {
    borderRadius: 0,
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: bgColor.grayLight,
    backgroundColor: bgColor.white,
    minHeight: 50,
  },
  selectedDropdownItem: {
    backgroundColor: bgColor.primary,
    borderBottomColor: bgColor.primary,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemText: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 12,
  },
  selectedItemTextInDropdown: {
    color: bgColor.white,
    fontWeight: '600',
  },
  selectedItemContainer: {
    marginRight: 8,
    marginTop: 8,
  },
  selectedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: bgColor.primary + '15', // 15% opacity
    borderColor: bgColor.primary,
    borderWidth: 1,
    borderRadius: 25,
    paddingHorizontal: 14,
    paddingVertical: 8,
    shadowColor: bgColor.primary,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  selectedItemText: {
    fontSize: 14,
    color: bgColor.primary,
    marginHorizontal: 8,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: bgColor.grayMedium,
    marginTop: 12,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: bgColor.grayMedium,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.8,
  },
});

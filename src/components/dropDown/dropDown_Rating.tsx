import React, {useState} from 'react';
import {StyleSheet, View, TouchableOpacity, Text} from 'react-native';
import {MultiSelect} from 'react-native-element-dropdown';
import CustomIcon from '../CustomIcon';
import bgColor from '../../utils/bgColor';

export default function DropDownRating() {
  const data = [
    {
      label: '5 Stars',
      value: '5',
      rating: 5,
      icon: 'star',
      description: 'Excellent',
    },
    {
      label: '4+ Stars',
      value: '4+',
      rating: 4,
      icon: 'star',
      description: 'Very Good',
    },
    {
      label: '3+ Stars',
      value: '3+',
      rating: 3,
      icon: 'star',
      description: 'Good',
    },
    {
      label: '2+ Stars',
      value: '2+',
      rating: 2,
      icon: 'star',
      description: 'Fair',
    },
    {
      label: '1+ Stars',
      value: '1+',
      rating: 1,
      icon: 'star',
      description: 'Any Rating',
    },
  ];

  const [selected, setSelected] = useState([]);

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <CustomIcon
          key={i}
          name="star"
          size={14}
          color={i <= rating ? bgColor.report : bgColor.grayLight}
        />,
      );
    }
    return stars;
  };

  const renderEmptyComponent = () => (
    <View style={customStyles.emptyContainer}>
      <CustomIcon name="star-border" size={48} color={bgColor.grayMedium} />
      <Text style={customStyles.emptyText}>No ratings found</Text>
      <Text style={customStyles.emptySubText}>
        Try adjusting your search terms
      </Text>
    </View>
  );

  const renderItem = (item: any, isSelected?: boolean) => {
    return (
      <View style={{paddingHorizontal: 10}}>
        <View
          style={[
            customStyles.dropdownItem,
            isSelected && customStyles.selectedDropdownItem,
          ]}>
          <View style={customStyles.itemContent}>
            <View style={customStyles.starsContainer}>
              {renderStars(item.rating)}
            </View>
            <View style={customStyles.itemTextContainer}>
              <Text
                style={[
                  customStyles.itemText,
                  isSelected && customStyles.selectedItemTextInDropdown,
                ]}>
                {item.label}
              </Text>
              <Text
                style={[
                  customStyles.itemSubText,
                  isSelected && customStyles.selectedItemSubTextInDropdown,
                ]}>
                {item.description}
              </Text>
            </View>
          </View>
          <CustomIcon
            name={isSelected ? 'check' : 'chevron-right'}
            size={16}
            color={isSelected ? bgColor.white : bgColor.grayMedium}
          />
        </View>
      </View>
    );
  };

  return (
    <View style={customStyles.container}>
      <View style={customStyles.headerContainer}>
        <View>
          <Text style={customStyles.label}>Select Rating Filter</Text>
          <Text style={customStyles.description}>
            Filter services by minimum rating requirements
          </Text>
        </View>
        {selected.length > 0 && (
          <TouchableOpacity
            style={customStyles.clearButton}
            onPress={() => setSelected([])}>
            <Text style={customStyles.clearButtonText}>
              Clear All ({selected.length})
            </Text>
          </TouchableOpacity>
        )}
      </View>
      <MultiSelect
        mode="modal"
        style={customStyles.dropdown}
        containerStyle={customStyles.dropdownContainer}
        placeholderStyle={customStyles.placeholderStyle}
        selectedTextStyle={customStyles.selectedTextStyle}
        inputSearchStyle={customStyles.inputSearchStyle}
        iconStyle={customStyles.iconStyle}
        itemContainerStyle={customStyles.itemContainer}
        data={data}
        labelField="label"
        valueField="value"
        placeholder="Select Rating"
        value={selected}
        search
        searchPlaceholder="Search ratings..."
        maxHeight={300}
        onChange={(item: any) => {
          setSelected(item);
        }}
        renderLeftIcon={() => (
          <CustomIcon name="star" size={20} color="#FFD700" />
        )}
        renderItem={(item, isSelected) => renderItem(item, isSelected)}
        renderRightIcon={() => (
          <CustomIcon name="expand-more" size={20} color={bgColor.grayMedium} />
        )}
        flatListProps={{
          ListEmptyComponent: renderEmptyComponent,
        }}
        renderSelectedItem={(item, unSelect) => (
          <TouchableOpacity
            onPress={() => unSelect && unSelect(item)}
            style={customStyles.selectedItemContainer}>
            <View style={customStyles.selectedItem}>
              <View style={customStyles.selectedStarsContainer}>
                {renderStars(item.rating)}
              </View>
              <Text style={customStyles.selectedItemText}>{item.label}</Text>
              <CustomIcon name="close" size={16} color={bgColor.grayMedium} />
            </View>
          </TouchableOpacity>
        )}
      />
    </View>
  );
}

const customStyles = StyleSheet.create({
  container: {
    marginVertical: 8,
    paddingHorizontal: 20,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  label: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  description: {
    fontSize: 14,
    color: bgColor.grayMedium,
    marginBottom: 8,
    lineHeight: 20,
  },
  clearButton: {
    backgroundColor: bgColor.alert + '15',
    borderColor: bgColor.alert,
    borderWidth: 1,
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  clearButtonText: {
    fontSize: 12,
    color: bgColor.alert,
    fontWeight: '600',
  },
  dropdown: {
    height: 50,
    backgroundColor: bgColor.white,
    borderRadius: 12,
    paddingHorizontal: 16,
     shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  placeholderStyle: {
    fontSize: 16,
    color: bgColor.grayMedium,
    marginLeft: 8,
  },
  selectedTextStyle: {
    fontSize: 16,
    color: '#333333',
    marginLeft: 8,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
    borderRadius: 8,
    paddingHorizontal: 12,
    backgroundColor: bgColor.grayLight,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  dropdownContainer: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: bgColor.grayLight,
    backgroundColor: bgColor.white,
    maxHeight: 300,
  },
  itemContainer: {
    borderRadius: 0,
  },
  dropdownItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 16,
    borderBottomWidth: 0.5,
    borderBottomColor: bgColor.grayLight,
    backgroundColor: bgColor.white,
    minHeight: 50,
  },
  selectedDropdownItem: {
    backgroundColor: '#fcf3cf',
    borderBottomColor: '#fcf3cf',
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 12,
  },
  itemTextContainer: {
    marginLeft: 4,
  },
  itemText: {
    fontSize: 16,
    color: '#333333',
    fontWeight: '500',
  },
  selectedItemTextInDropdown: {
    color: bgColor.white,
    fontWeight: '600',
  },
  itemSubText: {
    fontSize: 12,
    color: bgColor.grayMedium,
    marginTop: 2,
  },
  selectedItemSubTextInDropdown: {
    color: bgColor.white,
    opacity: 0.9,
  },
  selectedItemContainer: {
    marginRight: 8,
    marginTop: 8,
  },
  selectedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFD700' + '20',
    borderColor: '#FFD700',
    borderWidth: 1,
    borderRadius: 25,
    paddingHorizontal: 14,
    paddingVertical: 8,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  selectedStarsContainer: {
    flexDirection: 'row',
    marginRight: 6,
  },
  selectedItemText: {
    fontSize: 14,
    color: '#B8860B',
    marginHorizontal: 6,
    fontWeight: '600',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: bgColor.grayMedium,
    marginTop: 12,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: bgColor.grayMedium,
    marginTop: 4,
    textAlign: 'center',
    opacity: 0.8,
  },
});

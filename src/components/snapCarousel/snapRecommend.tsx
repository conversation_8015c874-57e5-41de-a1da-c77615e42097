import React from 'react';
import {StyleSheet, Text, View, Dimensions, Image} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import Images from '../../utils/ImageManager';
import styles from '../CustomStyle';
import {scale, verticalScale, moderateScale} from 'react-native-size-matters';

const {width} = Dimensions.get('window');

const data = [
  {
    id: '1',
    title: 'First Slide',
    image: Images.pro,
  },
  {
    id: '2',
    title: 'Second Slide',
    image: Images.pro,
  },
  {
    id: '3',
    title: 'Third Slide',
    image: Images.pro,
  },
];

export default function SnapRecommend() {
  return (
    <Carousel
      width={width}
      height={verticalScale(350)}
      data={data}
      mode="parallax"
      loop
      autoPlay
      autoPlayInterval={5000}
      style={{alignSelf: 'center'}}
      renderItem={({item}) => (
        <View key={item.id}>
          <Image source={item.image} style={styles.imageCarouselLogin} />
        </View>
      )}
    />
  );
}

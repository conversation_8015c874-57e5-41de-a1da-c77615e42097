import React from 'react';
import {StyleSheet, Text, View, Dimensions, Image} from 'react-native';
import Carousel from 'react-native-reanimated-carousel';
import Images from '../../utils/ImageManager';
import {scale, verticalScale, moderateScale} from 'react-native-size-matters';
import bgColor from '../../utils/bgColor';

const {width} = Dimensions.get('window');

const data = [
  {
    id: '1',
    title: 'First Slide',
    image: Images.pro,
  },
  {
    id: '2',
    title: 'Second Slide',
    image: Images.pro,
  },
  {
    id: '3',
    title: 'Third Slide',
    image: Images.pro,
  },
];

export default function SnapCarousel() {
  return (
    <Carousel
      width={width}
      height={verticalScale(150)}
      data={data}
      mode="parallax"
      loop
      autoPlay
      autoPlayInterval={5000}
      style={{alignSelf: 'center', backgroundColor: bgColor.white}}
      renderItem={({item}) => (
        <View style={{}} key={item.id}>
          <Image source={item.image} style={styles.image}/>
        </View>
      )}
    />
  );
}

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: 12,
  },
});

import {StyleSheet, Text, View} from 'react-native';
import React from 'react';
import styles from '../CustomStyle';
import {Avatar} from 'react-native-paper';
import Images from '../../utils/ImageManager';

export default function PhysicalDetail({navigation, route}: any) {
  const params = route.params || [];
  const item = params.dataDetail || [];
  console.log('item', item);

  return (
    <View style={styles.constainerDefault}>
      <View style={{flexDirection: 'row'}}>
        <Avatar.Image
          size={120}
          source={Images.test}
          style={{marginRight: 20}}
        />
        <View style={{flexDirection: 'column', flex: 1}}>
          <Text style={{fontSize: 14, fontWeight: 'bold', marginBottom: 5}}>
            พญ.พรทิพย์ พรมเมือง
          </Text>
          <Text style={{fontSize: 14, marginBottom: 5}}>
            เลขใบกระกอบ: ก111112
          </Text>
          <Text style={{fontSize: 14, marginBottom: 5}}>เชียงราย</Text>
        </View>
      </View>
    </View>
  );
}

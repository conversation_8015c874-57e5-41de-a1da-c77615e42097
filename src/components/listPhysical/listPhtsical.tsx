import {Text, View, FlatList, TouchableOpacity} from 'react-native';
import React, {useState, useRef} from 'react';
import {Avatar} from 'react-native-paper';
import Images from '../../utils/ImageManager';
import bgColor from '../../utils/bgColor';
import {useTheme as useRNPTheme, Button} from 'react-native-paper';
import {Rating} from 'react-native-elements';
import CustomIcon from '../CustomIcon';

export default function ListPhtsical({navigation}: any) {
  const data = [
    {
      id: '1',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 4.5,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '2',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 5,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '3',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 2,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '4',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 2.5,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '5',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 3,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '6',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 3.5,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '7',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 1,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '8',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 1.5,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '9',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 5,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
    {
      id: '10',
      users: 'พญ.พรทิพย์ พรมเมือง',
      stars: 2,
      image: Images.test,
      location: 'เชียงราย',
      price: '1,200',
      signatureNumber: 'ก111112',
      Language: {
        thai: true,
        english: true,
        China: false,
      },
    },
  ];
  const goFilter = () => {
    navigation.navigate('FilterPhysical');
  };
  const goDetail = (item: any) => {
    navigation.navigate('Physical Detail', {dataDetail: item});
  };
  const headerFlatlist = () => (
    <View style={{paddingHorizontal: 20, marginBottom: 20}}>
      <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
        <Text style={{fontSize: 16, fontWeight: 'bold'}}>
          Physical therapist near you
        </Text>
        <View style={{flexDirection: 'row'}}>
          <TouchableOpacity onPress={() => navigation.navigate('Search')}>
            <CustomIcon name="search" size={24} color="gray" />
          </TouchableOpacity>
          <View style={{margin: 5}} />
          <TouchableOpacity onPress={goFilter}>
            <CustomIcon name="filter-list" size={24} color="gray" />
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
  const renderItem = ({item}: any) => (
    <Button
      onPress={goDetail}
      style={{borderRadius: 0, backgroundColor: bgColor.white}}
      mode="contained">
      <View style={{flexDirection: 'row'}}>
        <Avatar.Image size={40} source={item.image} style={{marginRight: 10}} />
        <View style={{flexDirection: 'column', flex: 1}}>
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <Text style={{fontSize: 14, fontWeight: 'bold', marginBottom: 5}}>
              {item.users}
            </Text>
            <Text style={{fontSize: 14, fontWeight: 'bold'}}>
              {item.price} ฿
            </Text>
          </View>
          <Text style={{fontSize: 14, marginBottom: 5}}>
            เลขใบกระกอบวิชาชีพ: {item.signatureNumber}
          </Text>
          <Text style={{fontSize: 14, marginBottom: 5}}>{item.location}</Text>
          <View style={{alignItems: 'flex-start'}}>
            <Rating startingValue={item.stars} imageSize={14} readonly />
          </View>
        </View>
      </View>
    </Button>
  );
  return (
    <>
      <FlatList
        data={data}
        renderItem={renderItem}
        style={{flex: 1}}
        removeClippedSubviews={false}
        keyExtractor={item => item.id}
        ListHeaderComponent={headerFlatlist}
      />
    </>
  );
}

import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  Platform,
  Image,
  ImageSourcePropType,
} from 'react-native';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import React, {useEffect, useState} from 'react';
import {useDispatch} from 'react-redux';
import {setInfo, setAccessToken} from '../../redux/action';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {webId, iosId} from '../../constants/clientId';

interface GoogleSignInProps {
  navigation?: any;
  onSignIn: (userData: any) => void;
  onError?: (error: any) => void;
  logo?: ImageSourcePropType;
}

export default function GoogleSignIn({
  navigation,
  onSignIn,
  onError,
  logo,
}: GoogleSignInProps) {
  const dispatch = useDispatch();
  const [isSigninInProgress, setIsSigninInProgress] = useState(false);

  useEffect(() => {
    // Make sure these client IDs are correct and match your Google Cloud Console
    const webClientId = webId;
    const iosClientId = iosId;

    console.log('🔧 Configuring Google Sign-In...');
    console.log('📱 Platform:', Platform.OS);
    console.log('🌐 Web Client ID:', webClientId);
    console.log('🍎 iOS Client ID:', iosClientId);

    GoogleSignin.configure({
      webClientId: webClientId,
      offlineAccess: true,
      iosClientId: Platform.OS === 'ios' ? iosClientId : undefined,
    });

    console.log('✅ Google Sign-In configured successfully');
  }, []);

  const signIn = async () => {
    if (isSigninInProgress) return;

    try {
      setIsSigninInProgress(true);

      // Check if your device supports Google Play Services
      await GoogleSignin.hasPlayServices({showPlayServicesUpdateDialog: true});

      // Sign in
      const userInfo = await GoogleSignin.signIn();
      console.log('Google Sign-In successful:', userInfo);
      await AsyncStorage.setItem('idToken', userInfo?.data?.idToken || '');
      await AsyncStorage.setItem('userInfo', JSON.stringify(userInfo));
      dispatch(setAccessToken(userInfo?.data?.idToken));
      dispatch(setInfo(userInfo));
      if (onSignIn) {
        onSignIn(userInfo);
      }
    } catch (error: any) {
      console.error('Detailed error:', JSON.stringify(error));

      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log('User cancelled the login flow');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        console.log('Sign in is in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log('Play services not available');
        Alert.alert(
          'Error',
          'Google Play services are not available on this device',
        );
      } else {
        console.log('Error during sign in:', error);
        Alert.alert(
          'Sign-In Error',
          `An error occurred during Google sign-in: ${
            error.message || 'Unknown error'
          }`,
        );
      }

      if (onError) {
        onError(error);
      }
    } finally {
      setIsSigninInProgress(false);
    }
  };

  return (
    <TouchableOpacity
      style={styles.googleButton}
      onPress={signIn}
      disabled={isSigninInProgress}>
      {logo && <Image source={logo} style={styles.logo} />}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  googleButton: {
    backgroundColor: '#f4f4f4',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 10,
  },
  logo: {
    width: 24,
    height: 24,
  },
});

import React from 'react';
import { Text, StyleSheet } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

interface IconProps {
  name: string;
  size: number;
  color: string;
}

// Create a component that uses the pre-built MaterialIcons component
const CustomIcon = ({ name, size, color }: IconProps) => {
  try {
    // First try to use the actual MaterialIcons component
    return <MaterialIcons name={name} size={size} color={color} />;
  } catch (error) {
    // Fallback to a text representation
    return (
      <Text style={[styles.iconFallback, { color, fontSize: size }]}>
        {name.charAt(0).toUpperCase()}
      </Text>
    );
  }
};

const styles = StyleSheet.create({
  iconFallback: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
});

export default CustomIcon;

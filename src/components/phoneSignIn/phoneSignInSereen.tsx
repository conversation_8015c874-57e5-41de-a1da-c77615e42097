import {
  StyleSheet,
  Text,
  View,
  Image,
  TextInput,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
// import styles from '../../components/CustomStyle';
import React, {useState, useRef} from 'react';
// import auth from '@react-native-firebase/auth';
import PhoneInput from 'react-native-phone-number-input';
import {useNavigation} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {setInfo, setAccessToken} from '../../redux/action';
import Images from '../../utils/ImageManager';
import {useTheme as useRNPTheme, Button} from 'react-native-paper';
import {scale, verticalScale, moderateScale} from 'react-native-size-matters';
import bgColor from '../../utils/bgColor';

export default function PhoneSignInScreen() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [confirm, setConfirm] = useState<any>(null);
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [resendTimeout, setResendTimeout] = useState(0);
  const phoneInputRef = useRef(null);
  const navigation = useNavigation();
  const dispatch = useDispatch();

  const signInWithPhoneNumber = async () => {
    setLoading(true);
    try {
      const formattedNumber =
        phoneInputRef.current?.getNumberAfterPossiblyEliminatingZero?.();
      const numberToSend = formattedNumber || phoneNumber;

      if (!numberToSend) {
        Alert.alert('Error', 'Please enter a valid phone number');
        return;
      }

      const confirmation = await auth().signInWithPhoneNumber(numberToSend);
      setConfirm(confirmation);
      startResendTimer();
      Alert.alert('Success', 'OTP has been sent to your phone number');
    } catch (error: any) {
      console.error('Phone verification error:', error);
      let errorMessage = 'Failed to send verification code';

      if (error.code === 'auth/invalid-phone-number') {
        errorMessage = 'Invalid phone number format';
      } else if (error.code === 'auth/too-many-requests') {
        errorMessage = 'Too many requests. Please try again later';
      } else if (error.code === 'auth/quota-exceeded') {
        errorMessage = 'SMS quota exceeded. Please try again later';
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const confirmCode = async () => {
    if (!confirm || code.length < 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit code');
      return;
    }

    setLoading(true);
    try {
      const result = await confirm.confirm(code);
      const token = await result.user.getIdToken();

      const userData = {
        user: {
          uid: result.user.uid,
          phoneNumber: result.user.phoneNumber,
          displayName: result.user.displayName || result.user.phoneNumber,
          photoURL: result.user.photoURL,
        },
        token: token,
        data: {
          user: {
            uid: result.user.uid,
            phone: result.user.phoneNumber,
            name: result.user.displayName || result.user.phoneNumber,
            photo: result.user.photoURL,
          },
          idToken: token,
        },
      };

      // Save to AsyncStorage
      //   await SafeAsyncStorage.setItem('idToken', token);
      //   await SafeAsyncStorage.setItem('userInfo', JSON.stringify(userData));

      // Update Redux store
      dispatch(setAccessToken(token));
      dispatch(setInfo(userData));

      Alert.alert('Success', 'You are now signed in!', [
        {
          text: 'OK',
          onPress: () => {
            (navigation as any).reset({
              index: 0,
              routes: [{name: 'Home'}],
            });
          },
        },
      ]);
    } catch (error: any) {
      console.error('Code verification error:', error);
      let errorMessage = 'Invalid verification code';

      if (error.code === 'auth/invalid-verification-code') {
        errorMessage = 'Invalid verification code. Please try again';
      } else if (error.code === 'auth/code-expired') {
        errorMessage =
          'Verification code has expired. Please request a new one';
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const startResendTimer = () => {
    let timeLeft = 30;
    setResendTimeout(timeLeft);

    const timer = setInterval(() => {
      timeLeft -= 1;
      setResendTimeout(timeLeft);

      if (timeLeft <= 0) {
        clearInterval(timer);
      }
    }, 1000);
  };

  const resendOTP = () => {
    if (resendTimeout > 0) return;
    signInWithPhoneNumber();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      style={styles.container}>
      <View style={{alignItems: 'center', marginBottom: 20}}>
        <Image
          source={Images.logoApp}
          style={{width: 250, height: 250}}
          resizeMode="contain"
        />
      </View>

      {!confirm ? (
        <View style={styles.phoneInputContainer}>
          <PhoneInput
            ref={phoneInputRef}
            defaultValue={phoneNumber}
            defaultCode="TH" // Default to Thailand, change as needed
            layout="first"
            onChangeFormattedText={setPhoneNumber}
            autoFocus
            containerStyle={styles.phoneInput}
            textContainerStyle={styles.phoneInputTextContainer}
            flagButtonStyle={styles.flagButton}
            textInputStyle={styles.phoneTextInput}
            codeTextStyle={styles.codeText}
          />

          {/* <TouchableOpacity
            style={styles.button}
            onPress={signInWithPhoneNumber}
            disabled={!phoneNumber}>
            <Text style={styles.buttonText}>Send Verification Code</Text>
          </TouchableOpacity> */}

          <Button
            mode="contained"
            style={styles.button}
            onPress={signInWithPhoneNumber}
            disabled={!phoneNumber}>
            <Text style={styles.buttonText}>Send Verification Code</Text>
          </Button>
        </View>
      ) : (
        <View style={styles.otpContainer}>
          <Text style={styles.instructions}>
            Enter the 6-digit code sent to {phoneNumber}
          </Text>

          <TextInput
            value={code}
            onChangeText={setCode}
            placeholder="******"
            keyboardType="number-pad"
            maxLength={6}
            style={styles.otpInput}
            autoFocus
          />

          <TouchableOpacity
            style={styles.button}
            onPress={confirmCode}
            disabled={code.length < 6}>
            <Text style={styles.buttonText}>Verify Code</Text>
          </TouchableOpacity>

          <TouchableOpacity onPress={resendOTP} disabled={resendTimeout > 0}>
            <Text
              style={[
                styles.resendText,
                resendTimeout > 0 && styles.resendDisabled,
              ]}>
              {resendTimeout > 0
                ? `Resend code in ${resendTimeout}s`
                : 'Resend code'}
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // justifyContent: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
    color: '#333',
  },
  phoneInputContainer: {
    width: '100%',
  },
  otpContainer: {
    width: '100%',
    alignItems: 'center',
  },
  phoneInput: {
    width: '100%',
    height: 60,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#ddd',
    marginBottom: 20,
  },
  phoneInputTextContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
  },
  flagButton: {
    width: 60,
    borderRightWidth: 1,
    borderColor: '#ddd',
  },
  phoneTextInput: {
    height: 50,
    color: '#333',
  },
  codeText: {
    color: '#333',
  },
  otpInput: {
    width: '100%',
    height: 50,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 20,
    fontSize: 18,
    textAlign: 'center',
  },
  instructions: {
    marginBottom: 20,
    color: '#666',
    textAlign: 'center',
  },
  button: {
    width: '100%',
    marginBottom: verticalScale(10),
    borderRadius: 8,
    backgroundColor: bgColor.primary,
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
  },
  resendText: {
    color: '#007bff',
    textAlign: 'center',
  },
  resendDisabled: {
    color: '#999',
  },
});

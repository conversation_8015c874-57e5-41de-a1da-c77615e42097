import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Image,
  ImageSourcePropType,
} from 'react-native';
import React from 'react';
import { appleAuth } from '@invertase/react-native-apple-authentication';

interface AppleSignInProps {
  navigation?: any;
  onSignIn: (userData: any) => void;
  onError?: (error: any) => void;
  logo?: ImageSourcePropType;
}

export default function AppleSignIn({
  navigation,
  onSignIn,
  onError,
  logo,
}: AppleSignInProps) {
  const signIn = async () => {
    try {
      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
      });
      // ส่งข้อมูลไป callback
      onSignIn && onSignIn(appleAuthRequestResponse);
    } catch (error) {
      onError && onError(error);
    }
  };

  return (
    <TouchableOpacity style={styles.appleButton} onPress={signIn}>
      {logo && <Image source={logo} style={styles.logo} />}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  appleButton: {
    backgroundColor: '#f4f4f4',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    // width: '50%',
    marginVertical: 10,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
  },
  logo: {
    width: 24,
    height: 24,
  },
});

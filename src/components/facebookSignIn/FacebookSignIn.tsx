import { View, Text, TouchableOpacity, Image, StyleSheet, ImageSourcePropType } from 'react-native'
import React from 'react'

interface FacebookSignInProps {
  navigation?: any;
  onSignIn: (userData: any) => void;
  onError?: (error: any) => void;
  logo?: ImageSourcePropType;
}

export default function FacebookSignIn({navigation, onSignIn, onError, logo}: FacebookSignInProps) {
  return (
    <TouchableOpacity 
      style={styles.facebookButton} 
      onPress={onSignIn}
    >
      {logo && <Image source={logo} style={styles.logo} />}
      {/* <Text style={styles.buttonText}>Sign in with Facebook</Text> */}
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  facebookButton: {
    backgroundColor: '#f4f4f4',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    marginLeft: 10,
  },
  logo: {
    width: 24,
    height: 24,
  }
})

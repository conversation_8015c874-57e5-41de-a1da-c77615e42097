import {StyleSheet, View, Text, Image, TouchableOpacity} from 'react-native';
import React, {useState, useEffect} from 'react';
import {Avatar} from 'react-native-paper';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import CustomIcon from '../CustomIcon';
import {useSelector} from 'react-redux';
import {createNativeStackNavigator} from '@react-navigation/native-stack';

// Tab screens
import Home from '../../screen/home/<USER>';
import Tarcking from '../../screen/home/<USER>';
import Message from '../../screen/home/<USER>';
import Notification from '../../screen/home/<USER>';
import Profile from '../../screen/home/<USER>';
import bgColor from '../../utils/bgColor';

export default function BottomTap() {
  const info = useSelector((state: any) => state.info);
  const profileName = info?.data?.user?.name || '';
  const imageProfile = info?.data?.user?.photo || '';
  const Tab = createBottomTabNavigator();
  const [notificationCount, setNotificationCount] = useState(3);
  const HomeStack = createNativeStackNavigator();

  function HomeStackScreen() {
    return (
      <HomeStack.Navigator>
        <HomeStack.Screen
          name={`Hi, ${profileName}`}
          component={Home}
          options={{
            headerLargeTitle: true,
            headerTransparent: false,
            headerBlurEffect: 'light',
            headerLargeTitleShadowVisible: true,
            headerTitleAlign: 'left',
            headerStyle: {backgroundColor: 'white'},
            headerLargeStyle: {backgroundColor: bgColor.white},
            headerTitleStyle: {
              color: 'black',
              fontSize: 14,
            },
            headerLargeTitleStyle: {
              color: 'black',
              fontSize: 24,
              fontWeight: 'bold',
            },
          }}
        />
      </HomeStack.Navigator>
    );
  }

  function ProfileStackScreen() {
    return (
      <HomeStack.Navigator>
        <HomeStack.Screen
          name={'Menu'}
          component={Profile}
          options={{
            headerLargeTitle: true,
            headerTransparent: false,
            headerBlurEffect: 'light',
            headerLargeTitleShadowVisible: true,
            headerTitleAlign: 'left',
            headerStyle: {backgroundColor: 'white'},
            headerLargeStyle: {backgroundColor: 'white'},
            headerTitleStyle: {
              color: 'black',
              fontSize: 14,
            },
            headerLargeTitleStyle: {
              color: 'black',
              fontSize: 24,
              fontWeight: 'bold',
            },
          }}
        />
      </HomeStack.Navigator>
    );
  }

  return (
    <Tab.Navigator
      initialRouteName="Home"
      screenOptions={({route}) => ({
        headerShown: false,
        tabBarShowLabel: true,
        tabBarActiveTintColor: '#0080ff',
        tabBarInactiveTintColor: 'gray',
        tabBarIcon: ({focused, color, size}) => {
          if (route.name === 'Home') {
            return <CustomIcon name="home" size={size} color={color} />;
          } else if (route.name === 'Tarcking') {
            return (
              <CustomIcon name="directions-car" size={size} color={color} />
            );
          } else if (route.name === 'Message') {
            return <CustomIcon name="chat" size={size} color={color} />;
          } else if (route.name === 'Notification') {
            return (
              <CustomIcon name="notifications" size={size} color={color} />
            );
          } else if (route.name === 'Account') {
            return <Avatar.Image size={size} source={{uri: imageProfile}} />;
          }

          // Default icon
          return <CustomIcon name="help" size={size} color={color} />;
        },
      })}>
      <Tab.Screen name="Home" component={HomeStackScreen} />
      <Tab.Screen
        name="Tarcking"
        component={Tarcking}
        options={{
          headerShown: false,
        }}
      />
      <Tab.Screen
        name="Message"
        component={Message}
        options={{
          headerShown: true,
          headerTitle: 'Message',
          tabBarBadge: notificationCount > 0 ? notificationCount : undefined,
          tabBarBadgeStyle: {
            backgroundColor: '#FF3B30',
            color: 'white',
            fontSize: 10,
          },
        }}
      />
      <Tab.Screen
        name="Notification"
        component={Notification}
        options={{
          headerShown: true,
          headerTitle: 'Notification',
          tabBarBadge: notificationCount > 0 ? notificationCount : undefined,
          tabBarBadgeStyle: {
            backgroundColor: '#FF3B30',
            color: 'white',
            fontSize: 10,
          },
        }}
      />
      <Tab.Screen name="Account" component={ProfileStackScreen} />
    </Tab.Navigator>
  );
}

const styles = StyleSheet.create({});

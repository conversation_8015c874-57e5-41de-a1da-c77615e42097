import {StyleSheet, Dimensions} from 'react-native';
import bgColor from '../utils/bgColor';
import {scale, verticalScale, moderateScale} from 'react-native-size-matters';
const screenWidth = Dimensions.get('window').width;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: bgColor.white,
  },
  constainerDefault: {
    flex: 1,
    padding: verticalScale(20),
    backgroundColor: bgColor.white,
  },
  continuePadding: {
    padding: verticalScale(20),
  },
  continueManageLogin: {
    paddingHorizontal: verticalScale(20),
  },
  containerRowCenter: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  containerMagnet: {
    bottom: verticalScale(20),
    position: 'absolute',
    alignItems: 'center',
    left: 0,
    right: 0,
  },
  containerDropDown: {
    height: verticalScale(40),
    marginTop: verticalScale(10),
    backgroundColor: bgColor.white,
    borderColor: bgColor.grayLight,
    borderRadius: 12,
    padding: 12,
    borderWidth: 2,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: verticalScale(20),
  },
  logoApp: {
    width: moderateScale(250),
    height: verticalScale(250),
  },
  marginBottom5: {
    margin: moderateScale(5),
  },
  title: {
    fontSize: 30,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  titleBody: {
    fontSize: 16,
    color: bgColor.grayMedium,
    marginBottom: verticalScale(10),
  },
  titleMembership: {
    fontSize: 14,
    color: bgColor.grayMedium,
    marginBottom: moderateScale(5),
  },
  titleMembershipGo: {
    fontSize: 14,
    color: bgColor.primary,
    marginLeft: moderateScale(5),
    textDecorationLine: 'underline',
  },
  titlePolicy: {
    fontSize: 14,
    color: bgColor.grayMedium,
    textAlign: 'center',
    marginBottom: moderateScale(5),
  },
  titlePolicyGo: {
    fontSize: 14,
    color: bgColor.primary,
    marginLeft: moderateScale(5),
    textDecorationLine: 'underline',
  },
  titleMagnet: {
    fontSize: 14,
    color: bgColor.grayMedium,
  },
  titleSettings: {
    fontSize: 14,
    paddingVertical: verticalScale(10),
    fontWeight: 'bold',
  },
  titleDefault: {
    fontSize: 14,
  },
  emaiPhonelButton: {
    width: '100%',
    marginBottom: verticalScale(10),
    borderRadius: 8,
    backgroundColor: bgColor.primary,
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginVertical: verticalScale(10),
  },
  divider: {
    flex: 1,
    height: verticalScale(1),
  },
  orText: {
    marginHorizontal: moderateScale(10),
    color: bgColor.grayMedium,
  },
  googleContainer: {
    width: '100%',
    alignItems: 'center',
  },
  skipButton: {
    marginTop: verticalScale(20),
    flexDirection: 'row',
    justifyContent: 'center',
  },
  buttonSignIn: {
    marginTop: moderateScale(10),
    backgroundColor: bgColor.success,
  },
  createAccountButton: {
    marginTop: moderateScale(10),
    borderColor: bgColor.success,
    borderWidth: 1,
  },
  input: {
    marginBottom: verticalScale(10),
    backgroundColor: 'white',
  },
  inputOutline: {
    borderColor: bgColor.success,
  },
  continueCardSetting: {
    marginTop: verticalScale(10),
    backgroundColor: bgColor.white,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  buttomAccount: {
    backgroundColor: bgColor.white,
    borderRadius: 10,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,

    elevation: 3,
  },
  buttomRowSetting: {
    flex: 1,
    backgroundColor: bgColor.white,
    borderRadius: 10,
    alignItems: 'flex-start',
  },
  buttomLogout: {
    flex: 1,
    backgroundColor: bgColor.white,
    borderRadius: 10,
    alignItems: 'center',
  },
  cardSettings: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  cardLanguage: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: verticalScale(20),
    paddingVertical: verticalScale(10),
  },
  logo: {
    width: moderateScale(24),
    height: moderateScale(24),
    marginLeft: moderateScale(5),
  },
  placeholderStyle: {
    fontSize: 14,
    marginLeft: moderateScale(5),
  },
  selectedTextStyle: {
    fontSize: 14,
  },
  iconStyle: {
    width: moderateScale(20),
    height: moderateScale(20),
  },
  inputSearchStyle: {
    height: verticalScale(40),
    fontSize: 14,
    borderRadius: 8,
  },
  icon: {
    marginRight: moderateScale(5),
  },
  item: {
    padding: moderateScale(10),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedStyle: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 14,
    backgroundColor: bgColor.grayLight,
    borderWidth: 1,
    borderColor: bgColor.success,
    marginTop: moderateScale(10),
    marginRight: moderateScale(10),
    paddingHorizontal: moderateScale(10),
    paddingVertical: moderateScale(10),
  },
  textSelectedStyle: {
    marginRight: moderateScale(5),
    fontSize: 16,
  },
  imageCarouselLogin: {
    width: '100%',
    height: verticalScale(350),
    borderRadius: 12,
    marginBottom: verticalScale(10),
  },
});

export default styles;

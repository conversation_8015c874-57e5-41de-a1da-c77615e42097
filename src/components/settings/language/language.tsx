import {View, Text, Switch, Image} from 'react-native';
import React, {useState} from 'react';
import Images from '../../../utils/ImageManager';
import styles from '../../CustomStyle';
import bgColor from '../../../utils/bgColor';

export default function Language() {
  const [isLocation, setLocation] = useState(false);
  const toggleLocation = () => setLocation(previousState => !previousState);
  return (
    <View style={styles.constainerDefault}>
      <View>
        <Text style={{}}>Select Language</Text>
        <Text style={{}}>
          Choose one or more language for your
        </Text>
      </View>
      <View style={styles.continueCardSetting}>
        <View style={styles.cardLanguage}>
          <Image style={styles.logo} source={Images.china} />
          <View style={{margin: 5}} />
          <Text style={[styles.titleDefault, {flex: 1}]}>China</Text>
          <Switch
            trackColor={{false: bgColor.grayLight, true: bgColor.success}}
            thumbColor={isLocation ? bgColor.white : bgColor.grayLight}
            onValueChange={toggleLocation}
            value={isLocation}
          />
        </View>

        <View style={styles.cardLanguage}>
          <Image style={styles.logo} source={Images.thailand} />
          <View style={{margin: 5}} />
          <Text style={[styles.titleDefault, {flex: 1}]}>Thailand</Text>
          <Switch
            trackColor={{false: bgColor.grayLight, true: bgColor.success}}
            thumbColor={isLocation ? bgColor.white : bgColor.grayLight}
            onValueChange={toggleLocation}
            value={isLocation}
          />
        </View>

        <View style={styles.cardLanguage}>
          <Image style={styles.logo} source={Images.unitedKingdom} />
          <View style={{margin: 5}} />
          <Text style={[styles.titleDefault, {flex: 1}]}>United-Kingdom</Text>
          <Switch
            trackColor={{false: bgColor.grayLight, true: bgColor.success}}
            thumbColor={isLocation ? bgColor.white : bgColor.grayLight}
            onValueChange={toggleLocation}
            value={isLocation}
          />
        </View>
      </View>
    </View>
  );
}

import {ScrollView, View, Text, TouchableOpacity, Switch} from 'react-native';
import React, {useState, useEffect} from 'react';
import styles from '../CustomStyle';
import CustomIcon from '../../components/CustomIcon';
import bgColor from '../../utils/bgColor';
import VersionApp from '../version/versionApp';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function SettingsApp({navigation}: any) {
  //Redux
  const info = useSelector((state: any) => state.info);
  //State
  const [isNotifacation, setNotification] = useState(false);
  const toggleLocation = () => setNotification(previousState => !previousState);
  //Function
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const notificationPermission = await AsyncStorage.getItem(
          'notificationPermission',
        );

        console.log('Loaded notificationPermission:', notificationPermission);

        // อัปเดตสถานะ Switch
        setNotification(notificationPermission === 'granted');
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    };

    loadSettings();
  }, []);

  return (
    <ScrollView style={styles.constainerDefault}>
      <Text style={styles.titleSettings}>General</Text>
      <View style={styles.continueCardSetting}>
        <TouchableOpacity
          style={styles.cardSettings}
          onPress={() => navigation.navigate('Language')}>
          <Text style={styles.titleDefault}>Language</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cardSettings}
          onPress={() => navigation.navigate('Display')}>
          <Text style={styles.titleDefault}>Display Settings</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.cardSettings}>
          <Text style={styles.titleDefault}>Permission Settings</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>

        <View style={styles.cardSettings}>
          <Text style={styles.titleDefault}>Location</Text>
          <Switch
            trackColor={{false: bgColor.grayLight, true: bgColor.success}}
            thumbColor={isNotifacation ? bgColor.white : bgColor.grayLight}
            onValueChange={toggleLocation}
            value={isNotifacation}
          />
        </View>

         <View style={styles.cardSettings}>
          <Text style={styles.titleDefault}>Notification</Text>
          <Switch
            trackColor={{false: bgColor.grayLight, true: bgColor.success}}
            thumbColor={isNotifacation ? bgColor.white : bgColor.grayLight}
            onValueChange={toggleLocation}
            value={isNotifacation}
          />
        </View>
      </View>

      {info && info.data && info.data.idToken && (
        <>
          <Text style={styles.titleSettings}>Account & Secrity</Text>
          <View style={styles.continueCardSetting}>
            <TouchableOpacity style={styles.cardSettings}>
              <Text style={styles.titleDefault}>Face ID & Passcode</Text>
              <CustomIcon name="chevron-right" size={24} color="gray" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.cardSettings}>
              <Text style={styles.titleDefault}>Change login</Text>
              <CustomIcon name="chevron-right" size={24} color="gray" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.cardSettings}>
              <Text style={styles.titleDefault}>Delete Account</Text>
              <CustomIcon name="chevron-right" size={24} color="gray" />
            </TouchableOpacity>
          </View>
        </>
      )}

      <Text style={styles.titleSettings}>Other</Text>
      <View style={styles.continueCardSetting}>
        <TouchableOpacity style={styles.cardSettings}>
          <Text style={styles.titleDefault}>Apply for membership</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.cardSettings}>
          <Text style={styles.titleDefault}>About Health Care</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.cardSettings}
          onPress={() => navigation.navigate('Policy')}>
          <Text style={styles.titleDefault}>Privacy Policy</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>
        <View style={styles.cardSettings}>
          <Text style={styles.titleDefault}>App Version</Text>
          <VersionApp />
        </View>
      </View>
    </ScrollView>
  );
}

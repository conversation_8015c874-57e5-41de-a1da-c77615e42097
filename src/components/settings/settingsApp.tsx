import {
  ScrollView,
  View,
  Text,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import React, {useState, useEffect} from 'react';
import styles from '../CustomStyle';
import CustomIcon from '../../components/CustomIcon';
import bgColor from '../../utils/bgColor';
import VersionApp from '../version/versionApp';
import {useSelector} from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {requestLocationPermission} from '../../utils/locationService';

export default function SettingsApp({navigation}: any) {
  //Redux
  const info = useSelector((state: any) => state.info);
  //State
  const [isNotifacation, setNotification] = useState(false);
  const [isLocation, setLocation] = useState(false);
  const toggleNotification = () =>
    setNotification(previousState => !previousState);

  const toggleLocation = async () => {
    if (!isLocation) {
      // If location is currently off, request permission
      Alert.alert(
        '📍 Location Access',
        'Health Care Mobile needs location access to:\n\n• Show nearby health services\n• Save your location preferences\n• Provide personalized recommendations',
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Allow Location',
            onPress: async () => {
              try {
                const granted = await requestLocationPermission();
                await AsyncStorage.setItem(
                  'locationPermission',
                  granted ? 'granted' : 'denied',
                );
                setLocation(granted);

                if (granted) {
                  Alert.alert(
                    '✅ Success!',
                    'Location access enabled! You can now see nearby health services.',
                  );
                } else {
                  Alert.alert(
                    'Permission Denied',
                    'Location permission was denied. You can enable it later in your device settings.',
                  );
                }
              } catch (error) {
                console.error('Error requesting location permission:', error);
                Alert.alert('Error', 'Failed to request location permission.');
              }
            },
          },
        ],
      );
    } else {
      // If location is currently on, just turn it off (don't revoke system permission)
      setLocation(false);
      await AsyncStorage.setItem('locationPermission', 'denied');
      Alert.alert(
        'Location Disabled',
        'Location access has been disabled for this app.',
      );
    }
  };
  //Function
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // Load notification permission
        const notificationPermission = await AsyncStorage.getItem(
          'notificationPermission',
        );
        console.log('Loaded notificationPermission:', notificationPermission);
        setNotification(notificationPermission === 'granted');

        // Load location permission
        const locationPermission = await AsyncStorage.getItem(
          'locationPermission',
        );
        console.log('Loaded locationPermission:', locationPermission);

        // Set location switch based on permission status
        if (locationPermission === 'granted') {
          setLocation(true);
        } else {
          setLocation(false);
          // If never requested, set default status
          if (!locationPermission) {
            await AsyncStorage.setItem('locationPermission', 'not_requested');
          }
        }
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    };

    loadSettings();
  }, []);

  return (
    <ScrollView style={styles.constainerDefault}>
      <Text style={styles.titleSettings}>General</Text>
      <View style={styles.continueCardSetting}>
        <TouchableOpacity
          style={styles.cardSettings}
          onPress={() => navigation.navigate('Language')}>
          <Text style={styles.titleDefault}>Language</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.cardSettings}
          onPress={() => navigation.navigate('Display')}>
          <Text style={styles.titleDefault}>Display Settings</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>
      </View>

      <Text style={styles.titleSettings}>Permission</Text>
      <View style={styles.continueCardSetting}>
        <View style={styles.cardSettings}>
          <Text style={styles.titleDefault}>Location</Text>
          <Switch
            trackColor={{false: bgColor.grayLight, true: bgColor.success}}
            thumbColor={isLocation ? bgColor.white : bgColor.grayLight}
            onValueChange={toggleLocation}
            value={isLocation}
          />
        </View>

        <View style={styles.cardSettings}>
          <Text style={styles.titleDefault}>Notification</Text>
          <Switch
            trackColor={{false: bgColor.grayLight, true: bgColor.success}}
            thumbColor={isNotifacation ? bgColor.white : bgColor.grayLight}
            onValueChange={toggleNotification}
            value={isNotifacation}
          />
        </View>
      </View>

      {info && info.data && info.data.idToken && (
        <>
          <Text style={styles.titleSettings}>Account & Secrity</Text>
          <View style={styles.continueCardSetting}>
            <TouchableOpacity style={styles.cardSettings}>
              <Text style={styles.titleDefault}>Face ID & Passcode</Text>
              <CustomIcon name="chevron-right" size={24} color="gray" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.cardSettings}>
              <Text style={styles.titleDefault}>Change login</Text>
              <CustomIcon name="chevron-right" size={24} color="gray" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.cardSettings}>
              <Text style={styles.titleDefault}>Delete Account</Text>
              <CustomIcon name="chevron-right" size={24} color="gray" />
            </TouchableOpacity>
          </View>
        </>
      )}

      <Text style={styles.titleSettings}>Other</Text>
      <View style={styles.continueCardSetting}>
        <TouchableOpacity style={styles.cardSettings}>
          <Text style={styles.titleDefault}>Apply for membership</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.cardSettings}>
          <Text style={styles.titleDefault}>About Health Care</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.cardSettings}
          onPress={() => navigation.navigate('Policy')}>
          <Text style={styles.titleDefault}>Privacy Policy</Text>
          <CustomIcon name="chevron-right" size={24} color="gray" />
        </TouchableOpacity>
        <View style={styles.cardSettings}>
          <Text style={styles.titleDefault}>App Version</Text>
          <VersionApp />
        </View>
      </View>
    </ScrollView>
  );
}

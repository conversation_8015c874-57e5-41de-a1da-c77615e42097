import React, {useEffect} from 'react';
import CustomIcon from './src/components/CustomIcon';
import {ThemeProvider} from './src/utils/ThemeContext';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  TouchableOpacity,
  useColorScheme,
  Platform,
} from 'react-native';
import notifee, {AuthorizationStatus} from '@notifee/react-native';
import {PermissionsAndroid} from 'react-native';
import {
  Provider as PaperProvider,
  MD3DarkTheme,
  MD3LightTheme,
} from 'react-native-paper';
import {Provider} from 'react-redux';
import redux from './src/redux/store';
import AuthLoading from './src/utils/authLoading';
import { requestLocationPermission } from './src/utils/locationService';
//Page
import Selectloging from './src/screen/loging/Select_loging';
import SettingsApp from './src/components/settings/settingsApp';
import Policy from './src/components/privacy-policy/policy';
import Language from './src/components/settings/language/language';
import Display from './src/components/settings/display/display';
import BottomTap from './src/components/bottomTap/bottomTap';
import Search from './src/screen/search/Search';
import FilterPhysical from './src/components/filter/filterPhysical';
import PhoneSignInSereen from './src/components/phoneSignIn/phoneSignInSereen';
import PhysicalDetail from './src/components/detail/physical_detail';


export default function App() {
  const Stack = createNativeStackNavigator();
  const colorScheme = useColorScheme();
  const paperTheme = colorScheme === 'dark' ? MD3DarkTheme : MD3LightTheme;

  useEffect(() => {
    const checkPermissions = async () => {
      try {
        // Request notification permissions
        const notificationGranted = await requestNotificationPermissions();
        await AsyncStorage.setItem(
          'notificationPermission',
          notificationGranted ? 'granted' : 'denied',
        );
        if (!notificationGranted) {
          console.log('Notification permission not granted.');
        }

        // Request location permissions
        const locationGranted = await requestLocationPermission();
        await AsyncStorage.setItem(
          'locationPermission',
          locationGranted ? 'granted' : 'denied',
        );
        if (locationGranted) {
          console.log('✅ Location permission granted');
        } else {
          console.log('❌ Location permission not granted');
        }
      } catch (error) {
        console.error('Error checking permissions:', error);
      }
    };
    checkPermissions();
    // getToken();
  }, []);
  const requestNotificationPermissions = async (): Promise<boolean> => {
    try {
      if (Platform.OS === 'ios') {
        const settings = await notifee.requestPermission();
        if (settings.authorizationStatus < AuthorizationStatus.AUTHORIZED) {
          console.log('iOS: Notification permission denied');
          return false;
        }
        return true;
      } else {
        const permission: any =
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS;
        const status = await PermissionsAndroid.request(permission);
        if (status === PermissionsAndroid.RESULTS.GRANTED) {
          console.log('Android: Notification permission granted');
          return true;
        } else {
          console.log('Android: Notification permission denied');
          return false;
        }
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      return false;
    }
  };



  return (
    <Provider store={redux}>
      <PaperProvider theme={paperTheme}>
        <ThemeProvider>
          <NavigationContainer>
            <Stack.Navigator initialRouteName="AuthLoading">
              <Stack.Screen
                name="AuthLoading"
                component={AuthLoading}
                options={{headerShown: false}}
              />
              <Stack.Screen
                name="Selectloging"
                component={Selectloging}
                options={({navigation}) => ({
                  headerTitle: 'Login',
                  headerRight: () => (
                    <TouchableOpacity
                      onPress={() => navigation.navigate('SettingsApp')}>
                      <CustomIcon
                        name="manage-accounts"
                        size={24}
                        color="gray"
                      />
                    </TouchableOpacity>
                  ),
                })}
              />
              <Stack.Screen
                name="Home"
                component={BottomTap}
                options={{
                  headerTitle: '',
                  headerShown: false,
                  headerBackVisible: false,
                }}
              />
              <Stack.Screen
                name="SettingsApp"
                component={SettingsApp}
                options={{
                  headerTitle: 'Settings',
                  headerBackVisible: true,
                  headerLargeTitle: true,
                  headerTransparent: false,
                  headerBlurEffect: 'light',
                  headerLargeTitleShadowVisible: true,
                  headerTitleAlign: 'left',
                  headerStyle: {backgroundColor: 'white'},
                  headerLargeStyle: {backgroundColor: 'white'},
                  headerTitleStyle: {color: 'black'},
                  headerLargeTitleStyle: {color: 'black'},
                }}
              />
              <Stack.Screen
                name="Policy"
                component={Policy}
                options={{
                  headerTitle: 'Privacy Policy',
                  headerBackVisible: true,
                  headerLargeTitle: true,
                  headerTransparent: false,
                  headerBlurEffect: 'light',
                  headerLargeTitleShadowVisible: true,
                  headerTitleAlign: 'left',
                  headerStyle: {backgroundColor: 'white'},
                  headerLargeStyle: {backgroundColor: 'white'},
                  headerTitleStyle: {color: 'black'},
                  headerLargeTitleStyle: {color: 'black'},
                }}
              />
              <Stack.Screen
                name="Language"
                component={Language}
                options={{
                  headerTitle: 'Language',
                  headerBackVisible: true,
                  headerLargeTitle: true,
                  headerTransparent: false,
                  headerBlurEffect: 'light',
                  headerLargeTitleShadowVisible: true,
                  headerTitleAlign: 'left',
                  headerStyle: {backgroundColor: 'white'},
                  headerLargeStyle: {backgroundColor: 'white'},
                  headerTitleStyle: {color: 'black'},
                  headerLargeTitleStyle: {color: 'black'},
                }}
              />
              <Stack.Screen
                name="Display"
                component={Display}
                options={{
                  headerTitle: 'Display Settings',
                  headerBackVisible: true,
                  headerLargeTitle: true,
                  headerTransparent: false,
                  headerBlurEffect: 'light',
                  headerLargeTitleShadowVisible: true,
                  headerTitleAlign: 'left',
                  headerStyle: {backgroundColor: 'white'},
                  headerLargeStyle: {backgroundColor: 'white'},
                  headerTitleStyle: {color: 'black'},
                  headerLargeTitleStyle: {color: 'black'},
                }}
              />
              <Stack.Screen
                name="PhoneSignInSereen"
                component={PhoneSignInSereen}
                options={{
                  headerTitle: 'Phone SignIn',
                  headerBackVisible: true,
                  headerLargeTitle: true,
                  headerTransparent: false,
                  headerBlurEffect: 'light',
                  headerLargeTitleShadowVisible: true,
                  headerTitleAlign: 'left',
                  headerStyle: {backgroundColor: 'white'},
                  headerLargeStyle: {backgroundColor: 'white'},
                  headerTitleStyle: {color: 'black'},
                  headerLargeTitleStyle: {color: 'black'},
                }}
              />
              <Stack.Screen
                name="FilterPhysical"
                component={FilterPhysical}
                options={{
                  headerTitle: 'Filter Physical',
                  headerBackVisible: true,
                  headerLargeTitle: true,
                  headerTransparent: false,
                  headerBlurEffect: 'light',
                  headerLargeTitleShadowVisible: true,
                  headerTitleAlign: 'left',
                  headerStyle: {backgroundColor: 'white'},
                  headerLargeStyle: {backgroundColor: 'white'},
                  headerTitleStyle: {color: 'black'},
                  headerLargeTitleStyle: {color: 'black'},
                }}
              />
              <Stack.Screen
                name="Search"
                component={Search}
                options={{
                  headerTitle: 'Search',
                  headerBackVisible: true,
                  headerSearchBarOptions: {
                    placeholder: 'Search...',
                    hideWhenScrolling: false,
                    autoCapitalize: 'none',
                    barTintColor: '#f4f4f4',
                    tintColor: '#0080ff',
                  },
                }}
              />
              <Stack.Screen
                name="Physical Detail"
                component={PhysicalDetail}
                options={{
                  headerTitle: 'Record Phtsical',
                  headerBackVisible: true,
                  headerLargeTitle: true,
                  headerTransparent: false,
                  headerBlurEffect: 'light',
                  headerLargeTitleShadowVisible: true,
                  headerTitleAlign: 'left',
                  headerStyle: {backgroundColor: 'white'},
                  headerLargeStyle: {backgroundColor: 'white'},
                  headerTitleStyle: {color: 'black'},
                  headerLargeTitleStyle: {color: 'black'},
                }}
              />
            </Stack.Navigator>
          </NavigationContainer>
        </ThemeProvider>
      </PaperProvider>
    </Provider>
  );
}



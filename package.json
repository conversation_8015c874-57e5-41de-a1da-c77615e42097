{"name": "Health", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^5.1.6", "@invertase/react-native-apple-authentication": "^2.4.1", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/checkbox": "^0.5.20", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@reduxjs/toolkit": "^2.8.2", "@rneui/themed": "^4.0.0-rc.8", "@types/react-native-snap-carousel": "^3.8.11", "lucide-react-native": "^0.525.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-actions-sheet": "^0.9.7", "react-native-device-info": "^14.0.4", "react-native-element-dropdown": "^2.12.4", "react-native-elements": "^3.4.3", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.26.0", "react-native-maps": "^1.24.3", "react-native-pager-view": "^6.8.1", "react-native-paper": "^5.14.5", "react-native-phone-number-input": "^2.1.0", "react-native-reanimated": "^3.18.0", "react-native-reanimated-carousel": "^4.0.2", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.11.1", "react-native-size-matters": "^0.4.2", "react-native-vector-icons": "^10.2.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.3", "@react-native/eslint-config": "0.79.3", "@react-native/metro-config": "0.79.3", "@react-native/typescript-config": "0.79.3", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}
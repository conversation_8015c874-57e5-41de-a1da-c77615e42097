<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>731979155438-p1vklpe7b5l5aibq6m49fd5b8lrfcbm9.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.731979155438-p1vklpe7b5l5aibq6m49fd5b8lrfcbm9</string>
	<key>API_KEY</key>
	<string>AIzaSyAMzfD7VhLKe0NZF9rUrneU2nM4TrheRvA</string>
	<key>GCM_SENDER_ID</key>
	<string>731979155438</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.healthcare</string>
	<key>PROJECT_ID</key>
	<string>health-care-8d6c1</string>
	<key>STORAGE_BUCKET</key>
	<string>health-care-8d6c1.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:731979155438:ios:fba64e27cbafec9e7ef0ff</string>
</dict>
</plist>